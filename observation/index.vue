<template>
  <base-layout
    nav-title="观察记录"
    containerClass="template-container"
    navBgColor="transparent"
    :footerStyle="{
      padding: 0,
      backgroundColor: '#fff',
    }"
  >
    <view class="observation-bg">
      <view class="observation">
        <view class="observation-header">
          <view class="observation-header-left">选择记录场景</view>
        </view>
      </view>
      <view class="observation-list">
        <view
          class="observation-list-item"
          :class="{ 'observation-list-item-active': isActive == item.id }"
          @click="onAddScene(item)"
          v-for="item in scenes"
          :key="item.id"
          >{{ item.label }}</view
        >
      </view>
    </view>
    <template #footer>
      <view class="observation-footer">
        <up-button text="手动生成" @tap="onGoToScene" shape="circle" />
        <up-button
          text="AI生成"
          @tap="onGoToScene('ai')"
          shape="circle"
          color="#367CFF"
        />
      </view>
    </template>
  </base-layout>
</template>
<script setup>
import BaseLayout from "@/components/base-layout/base-layout.vue";
import { ref } from "vue";
import { getDICT, sharePageObj } from "@/utils";
import { onShareAppMessage, onLoad } from "@dcloudio/uni-app";

onShareAppMessage(() => sharePageObj());

// 学习 生活 区域游戏 体育活动 户外自主游戏

const scenes = ref([]);
let isActive = ref(-1);

// 页面参数
const childId = ref('');
const childName = ref('');

// 页面加载
onLoad((options) => {
  childId.value = options.childId || '';
  childName.value = options.childName || '';
});
const onAddScene = (item) => {
  console.log(item);
  isActive.value = item.id;
};
const onGoToScene = (type) => {
  if (isActive.value == -1) {
    return uni.showToast({
      title: "请选择记录场景",
      icon: "error",
    });
  }
  let url = `/observation/updateObservation?observationRecordScene=${isActive.value}`;
  if (type == "ai") {
    url = `/observation/updateObservation?observationRecordScene=${isActive.value}&isAi=1`;
  }

  // 添加 childId 和 childName 参数
  if (childId.value) {
    url += `&childId=${childId.value}`;
  }
  if (childName.value) {
    url += `&childName=${encodeURIComponent(childName.value)}`;
  }

  uni.navigateTo({
    url,
  });
};

getDICT("all").then((dics) => {
  const ObservationRecordSceneEnumDesc = dics["ObservationRecordSceneEnumDesc"];
  console.log(ObservationRecordSceneEnumDesc, "ObservationRecordSceneEnumDesc");
  
  if (ObservationRecordSceneEnumDesc) {
    scenes.value = Object.keys(ObservationRecordSceneEnumDesc).map(
      (key) => {
        return {
          label: ObservationRecordSceneEnumDesc[key],
          id: key,
        };
      }
    );
  }
});

</script>
<style lang="scss" scoped>
.observation-bg {
  background: url("https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/courseDetails_ai_bg.png")
    no-repeat;
  background-size: 170rpx 175rpx;
  background-position: 22rpx 0rpx;
}

.observation {
  &-header {
    line-height: 175rpx;
    height: 175rpx;
    text-align: center;
  }

  &-footer {
    display: flex;
    padding: 16rpx 32rpx 0 32rpx;
    padding-bottom: env(safe-area-inset-bottom);
    // #ifdef H5
    padding-bottom: 16rpx;
    // #endif
    .u-button {
      width: 48%;
    }
  }
}

.observation-header-right {
  font-size: 30rpx;
  font-weight: 500;
  color: rgba(63, 121, 255, 1);
  text-align: center;
  margin-top: 60rpx;
}

.observation-list-item {
  line-height: 144rpx;
  text-align: center;
  margin-bottom: 24rpx;

  height: 144rpx;
  border-radius: 28rpx;
  background: #ffffff;
  box-shadow: 1rpx 1rpx 16rpx #eee;

  &:last-child {
    margin-bottom: 0;
  }

  &-active {
    border: 1px solid #367cff;
    color: #367cff;
    box-sizing: border-box;
  }

  &:active {
    background: #f5f5f5;
  }
}
</style>
