<template>
  <base-layout nav-title="观察记录" containerClass="template-container" navBgColor="transparent" :footerStyle="{
    display: 'none',
  }" :contentStyle="{
    padding: '0',
  }">
    <view class="addObservation">
      <view class="addObservation-title">
        <view class="addObservation-title-left">选择记录场景：{{
          getOptionLabelByKeyAndId(
            "observationType",
            formData.observationType || ""
          )
        }}</view>
        <!-- <view
          class="addObservation-title-right-box"
          @click="onShowPicker('observationRecordScene')"
        >
          <image
            class="addObservation-title-right"
            src="./switch.png"
            alt=""
          />
          切换
        </view> -->
      </view>

      <view class="addObservation-form">
        <up-form ref="formRef" labelPosition="top" labelWidth="140rpx" :model="formData" :rules="rules" :labelStyle="{
          fontSize: '30rpx',
          fontWeight: '600',
        }" labelAlign="left">
          <up-form-item class="child" :style="formItemStyle" label="记录班级" prop="schoolClassId"
            @click="onShowPicker('schoolClassId')">
            <view>
              {{
                getOptionLabelByKeyAndId(
                  "schoolClassId",
                  formData.schoolClassId
                )
              }}
              <text v-if="
                !getOptionLabelByKeyAndId(
                  'schoolClassId',
                  formData.schoolClassId
                )
              " class="placeholder">请输入记录班级</text>
            </view>

            <template #right>
              <up-icon name="arrow-down" />
            </template>
            <view class="mask" v-if="isChild" @click.stop.prevent="() => { }" />
          </up-form-item>

          <up-form-item class="child" :style="formItemStyle" label="观察对象" prop="children" required
            :disabled="!formData.schoolClassId" @click="onShowPicker('childIds')">
            <view>
              {{ formData.childNames.join(",") }}
              <text v-if="!formData.childNames.join(',')" class="placeholder">请输入观察对象</text>
            </view>
            <template #right>
              <up-icon name="arrow-down" />
            </template>
            <view class="mask" v-if="isChild" @click.stop.prevent="() => { }" />
          </up-form-item>

          <up-form-item :style="formItemStyle" label="观察时间" prop="observationTime" required>
            <uni-datetime-picker class="datetime-picker" :border="false" leftIcon="none" type="datetime"
              placeholder="请选择观察时间" v-model="formData.observationTime" @change="calendarConfirm" />
            <template #right v-if="!formData.observationTime">
              <up-icon name="arrow-down" />
            </template>
            <!-- <view>
              {{ formData.observationTime }}
              <text
                v-if="!formData.observationTime"
                class="placeholder"
                >请输入观察时间</text
              >
            </view>
            <template #right>
              <up-icon name="arrow-down" />
            </template> -->
          </up-form-item>

          <up-form-item :style="formItemStyle" label="观察地点" prop="observationLocation" required>
            <up-input :cursorSpacing="100" v-model="formData.observationLocation" placeholder="请输入观察地点" border="none" />
          </up-form-item>

          <up-form-item :style="formItemStyle" label="活动名称" prop="activityName" required>
            <up-input :cursorSpacing="100" v-model="formData.activityName" placeholder="请输入活动名称" border="none" />
          </up-form-item>

          <up-form-item v-if="!isAi" :style="formItemStyle" label="材料" prop="productDesc">
            <up-textarea :cursorSpacing="100" confirmType="none" v-model="formData.productDesc" placeholder="请输入材料"
              autoHeight maxlength="-1" border="none" />
          </up-form-item>

          <up-form-item v-if="isAi" :style="formItemStyle" label="活动梗概" prop="activityOutline" required>
            <up-textarea :cursorSpacing="100" height="200rpx" confirmType="none" v-model="formData.activityOutline"
              placeholder="幼儿在什么地方，用什么材料，做了什么，怎么做的，做了多长时间；幼儿遇到了什么问题，如何解决的；幼儿对谁说了什么；幼儿表情如何，有什么变化等。" autoHeight
              maxlength="-1" border="none" />
          </up-form-item>

          <up-form-item v-if="!isAi" :style="formItemStyle" label="背景" prop="observationBackground" labelWidth="100">
            <up-textarea :cursorSpacing="100" confirmType="none" v-model="formData.observationBackground"
              placeholder="请输入背景" autoHeight maxlength="-1" border="none" />
          </up-form-item>

          <up-form-item v-if="!isAi" :style="formItemStyle" label="目的" prop="observationPurpose" labelWidth="100">
            <up-textarea :cursorSpacing="100" confirmType="none" v-model="formData.observationPurpose"
              placeholder="请输入目的" autoHeight maxlength="-1" border="none" />
          </up-form-item>

          <up-form-item v-if="!isAi" label-position="top" :style="formItemStyle" label="观察内容" prop="content">
            <up-textarea :cursorSpacing="100" confirmType="none" v-model="formData.observationContent"
              placeholder="请输入观察内容" autoHeight maxlength="-1" border="none" />
          </up-form-item>

          <up-form-item label-position="top" :style="formItemStyle" label="照片" prop="picList" labelWidth="80rpx"
            required>
            <view>
              <view v-if="isAi" class="text-Empty uploadPlaceholder">
                照片将决定AI识别的准确度，建议上传如下几张照片：场景和材料、多张幼儿游戏过程、幼儿遇到的问题、幼儿表情、幼儿作品等。</view>
              <Upload type="image" :value="formData.picList" @callback="callback" @emitDelFile="delFile"
                :showDel="!disabled" :fileCategory="230">
                <view style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                ">
                  <image src="@/static/icon/u-icon.png" style="width: 37rpx; height: 31rpx; margin-bottom: 10rpx;" />
                  <text style="font-size: 21rpx; font-weight: 400; color: #808080">上传照片</text>
                </view>
              </Upload>
            </view>
          </up-form-item>
          <up-form-item v-if="visualMaps.analysis || formData.analysis" label-position="top" :style="formItemStyle"
            label="分析" prop="analysis">
            <up-textarea :cursorSpacing="100" v-model="formData.analysis" placeholder="请输入分析内容" border="none" autoHeight
              maxlength="-1" confirmType="none" />
          </up-form-item>

          <up-form-item v-else-if="!isAi" :style="formItemStyle" label="" prop="analysis">
            <view @click="visualMaps.analysis = true" class="selectable-content">
              + 分析
            </view>
          </up-form-item>

          <up-form-item v-if="visualMaps.support || formData.support" label-position="top" :style="formItemStyle"
            label="支持" prop="support">
            <up-textarea :cursorSpacing="100" v-model="formData.support" placeholder="请输入支持内容" border="none" autoHeight
              maxlength="-1" confirmType="none" />
          </up-form-item>

          <up-form-item v-else-if="!isAi" :style="formItemStyle" label="" prop="support">
            <view @click="visualMaps.support = true" class="selectable-content">
              + 支持
            </view>
          </up-form-item>

          <up-form-item v-if="visualMaps.reflection || formData.reflection" label-position="top" :style="formItemStyle"
            label="反思" prop="reflection">
            <up-textarea :cursorSpacing="100" v-model="formData.reflection" placeholder="请输入反思内容" border="none"
              autoHeight confirmType="none" maxlength="-1" />
          </up-form-item>

          <up-form-item v-else-if="!isAi" :style="formItemStyle" label="" prop="reflection">
            <view @click="visualMaps.reflection = true" class="selectable-content">
              + 反思
            </view>
          </up-form-item>
        </up-form>
        <!-- 核心经验 -->
        <template v-if="params.id && !isAi">
          <view class="matrixSelect-title">核心经验匹配</view>
          <view v-for="(item, index) in matric" :key="new Date().getTime() + index">
            <MatrixSelect v-if="item && matrix1List.length > 0" :matric="item" :matrixList="matrix1List" :index="index"
              @onDeleteMatrix="deleteMatric" @change="userChange" />
          </view>
          <view class="matrixSelect-add" @tap="addMatric">+ 新增核心经验</view>
        </template>
      </view>
    </view>
    <u-loading-page :loading="isPageLoading" />
    <view class="addObservation-buttons">
      <view @click="onSaveOb" class="addObservation-button-save">{{ isAi ? "AI生成" : "保存" }}</view>
    </view>
    <up-picker :show="isPickerShow" :defaultIndex="[0]" :columns="activeOption" @confirm="pickerConfirm"
      @cancel="isPickerShow = false" keyName="label"></up-picker>

    <niceui-popup-select ref="popupSelectRef" :value="children" :columns="activeOption[0]"
      :selectValue="optionsList.ClassnameSelectValue" :option="{ label: 'label', value: 'id' }" @confirm="pickerConfirm"
      :multiple="true"></niceui-popup-select>
  </base-layout>
</template>

<script setup>
import BaseLayout from "@/components/base-layout/base-layout.vue";
import NiceuiPopupSelect from "./niceui-popup-select/niceui-popup-select.vue";
import MatrixSelect from "./matrixSelect.vue";
import {
  scenes,
  getInitFormData,
  removeEmpty,
  childrenFilter,
  removeDuplicates,
  rules,
} from "./data";

import Upload from "@/components/Upload/Upload.vue";
import { onLoad } from "@dcloudio/uni-app";
import { getFileType } from "@/utils";
import { computed, ref, reactive, nextTick, onUnmounted } from "vue";
import { useQueryParams } from "@/common/hooks/useQueryParams.js";
import { getclassList } from "@/api";
import { getDICT } from "@/utils";
import { getChildrenList } from "@/api/children";
import {
  addObservation,
  addAIObservation,
  updateObservation,
  getObservationDetailById,
} from "./api";
import {
  updateCoreExpList
} from "./api/observation.js";
import dayjs from "dayjs";
import coreExp from "./coreExperience.js";

const children = ref(null);
const disabled = ref(false);
// 单选选择控制器
const isPickerShow = ref(false);
// const isCalendarShow = ref(false);
// 多选选择控制器
const popupSelectRef = ref();
// 分析 支持 反思显示控制
const visualMaps = reactive({
  analysis: false,
  support: false,
  reflection: false,
});

const isUserChange = ref(false); // 用户是否修改过
const isAi = ref(false); // 是否是ai生成
const isPageLoading = ref(false); // 页面加载中
const togglePopUpSelect = (show) => {
  if (show) {
    popupSelectRef.value.showPopup();
  } else {
    popupSelectRef.value.closePopup();
  }
};
/** URL query */
const { params } = useQueryParams("observationRecordScene", "id");
const { matric, matrix1List } = coreExp(params.value.id); // 核心经验相关功能
/** 表单 */
const formRef = ref(null);
/** 选择器选项列表 */
const columnsList = ref([[]]);
/** 表单数据 */
const formData = ref(
  getInitFormData({
    observationType: params.value.observationRecordScene,
  })
);

const isChild = ref(false);

/** 从地址上面获取选择了什么参数*/
onLoad((options) => {
  if (options.isAi) isAi.value = options.isAi == 1 ? true : false;
  if (options.observationRecordScene)
    formData.value.observationType = options.observationRecordScene;
  if (options.isChild) {
    isChild.value = true;
  }

  // 处理从儿童动态页面传入的参数
  if (options.childId && options.childName) {
    // 设置当前时间
    formData.value.observationTime = dayjs().format("YYYY-MM-DD HH:mm:ss");

    // 延迟设置观察对象，等待儿童列表加载完成
    setTimeout(() => {
      setChildFromParams(options.childId, options.childName);
    }, 1000);
  }
});

/** 图片  上传相关*/
const imageList1 = ref([]);
const callback = (list) => {
  formData.value.picList.push(...list);
};
const delFile = (item, index) => {
  formData.value.picList.splice(index, 1);
};

/** 选项 */
const optionsList = reactive({
  // 场景选项
  observationType: scenes,
  // 用户班级列表
  schoolClassId: [],
  // 当前班级学生列表
  childIds: [],
  // 观察对象选中的值
  ClassnameSelectValue: [],
  matrixList: [],
  oldChildren: [], // 缓存观察对象
});
/** 当前选项 */
const activeOptionKey = ref("");
/** 表单样式 */
const formItemStyle = `
  border-radius: 28rpx;
  padding: 0 28rpx;
  background: #fff;
  margin-bottom: 24rpx;
`;

onUnmounted(() => {
  // 清除缓存
  optionsList.oldChildren = [];
});

// 选择器确认选择
const pickerConfirm = ({ value, fullValue }) => {
  console.log(fullValue, "1111111111111111");
  console.log(activeOptionKey.value);
  const className = optionsList.schoolClassId.find(item => item.id == formData.value.schoolClassId)?.label;
  switch (activeOptionKey.value) {
    case "observationRecordScene":
      formData.value.observationType = value[0].id;
      break;
    case "schoolClassId":
      formData.value.schoolClassId = value[0].id;
      optionsList.oldChildren = formData.value.children;
      updateChildList();
      break;
    case "childIds":
      // 判断是否有缓存
      if (optionsList.oldChildren.length > 0) {
        // 去除 两个数组 重复的部分
        formData.value.children = removeDuplicates(
          optionsList.oldChildren,
          childrenFilter(fullValue, className)
        );
      } else {
        formData.value.children = childrenFilter(fullValue, className);
      }
      optionsList.ClassnameSelectValue = formData.value.children;
      formData.value.childNames = formData.value.children.map((item) => item.childName);
      togglePopUpSelect(false);
      break;
    default:
      break;
  }

  isPickerShow.value = false;
};

function getOptionLabelByKeyAndId(key, id) {
  if (typeof id === "string" || typeof id === "number") {
    if (!optionsList[key]) {
      return id;
    }
    const target = optionsList[key].find((item) => item.id == id);

    return target ? target.label : id;
  }

  if (Array.isArray(id)) {
    if (!optionsList[key]) {
      return id.join(",");
    }
    const ts = id
      .map((m) => optionsList[key].find((item) => item.id == m))
      .filter(Boolean);
    if (ts.length) {
      return ts.map((v) => v.label).join(",");
    }
    return "";
  }

  return id;
}

const activeOption = computed(() => {
  return [optionsList[activeOptionKey.value] || []];
});

const multipleKeys = ["childIds"];

function onShowPicker(key) {
  console.log(key, "key");
  if (key === "childIds" && !formData.value.schoolClassId) {
    uni.$u.toast("请先选择班级");
    return;
  }
  activeOptionKey.value = key;
  if (multipleKeys.includes(key)) {
    nextTick(() => {
      togglePopUpSelect(true);
    });
  } else {
    isPickerShow.value = true;
  }
  return;
}

function updateChildList() {
  getChildrenList({
    current: 1,
    pageSize: 500,
    classId: formData.value.schoolClassId || uni.getStorageSync("USER_INFO").currentClassId,
  }).then((response) => {
    optionsList.childIds = response.data.map((d) => ({
      ...d,
      label: d.title,
      name: d.title,
      checked: false,
    }));
  });
}

// 根据传入的参数设置观察对象
function setChildFromParams(childId, childName) {
  // 查找对应的儿童
  const targetChild = optionsList.childIds.find(child => child.id == childId);
  if (targetChild) {
    // 设置观察对象
    const className = optionsList.schoolClassId.find(item => item.id == formData.value.schoolClassId)?.label;
    formData.value.children = [childrenFilter(targetChild, className)];
    formData.value.childNames = [childName];
    optionsList.ClassnameSelectValue = formData.value.children;
  }
}
// 添加核心经验
const addMatric = () => {
  isUserChange.value = true;
  matric.value.push({
    relId: params.value.id,
    matchType: 'observation',
    matrix1Id: null,
    matrix1Name: null,
    matrix2Id: null,
    matrix2Name: null,
    matrix3Id: null,
    matrix3Name: null,
    targetId: null,
    targetName: null,
    isEdit: true,
  })
}


// 保存
const onSaveOb = () => {
  formRef.value
    .validate()
    .then((valid) => {
      if (valid) {
        uni.showLoading({
          title: "保存中...",
          mask: true,
        });
        // 判断属性里面是否为null
        console.log(matric.value);
        if (!isAi.value && matric.value.some((item) => Object.values(item).some((v) => v == null))) {
          uni.$u.toast("核心经验匹配请填写完整");
          return;
        }
        const fn = params.value.id
          ? updateObservation
          : isAi.value
            ? addAIObservation
            : addObservation;
        fn({
          ...removeEmpty(formData.value),
          picList: formData.value.picList.map((m) => m.serviceUri || m.uri),
        })
          .then(async (r) => {
            if (r.status === 0) {
              let falg = params.value.id ? await onSaveCore() : true;
              console.log("是否被触发：", falg)
              if (falg) {
                uni.showToast({
                  title: "保存成功",
                  icon: "none",
                });
                setTimeout(() => {
                  // 关闭页面 跳转到对应页面
                  if (isChild.value) {
                    uni.navigateBack();
                  } else {
                    uni.redirectTo({
                      url: "/observation/observationRecord",
                      success: function () {
                        uni.setStorageSync("isRef", true);
                      },
                    });
                  }
                }, 300);
              }

            } else {
              uni.showToast({
                title: r.error?.stack || "保存失败",
                icon: "none",
              });
            }
          })
          .finally(() => {
            uni.hideLoading();
          });

      } else {
        uni.$u.toast("有未填项，请检查！");
      }
    })
    .catch((e) => {
      console.log(e);
      console.log(formData.value.children);

      // 处理验证错误
      uni.$u.toast("校验失败");
    });
};

// 删除核心经验
const deleteMatric = (index) => {
  isUserChange.value = true;
  matric.value.splice(index, 1);
}

// 保存核心经验
const onSaveCore = async () => {
  if (isUserChange.value) {
    let data = {
      relId: Number(params.value.id),
      matchType: 'observation',
      list: []
    }
    data.list = matric.value.map((item) => {
      item.schoolId = uni.getStorageSync("USER_INFO").currentSchoolId;
      return {
        relId: Number(params.value.id),
        matchType: 'observation',
        matrix1Id: item.matrix1Id,
        matrix1Name: item.matrix1Name,
        matrix2Id: item.matrix2Id,
        matrix2Name: item.matrix2Name,
        matrix3Id: item.matrix3Id,
        matrix3Name: item.matrix3Name,
        targetId: item.targetId,
        targetName: item.targetName,
      }
    })
    try {
      let res = await updateCoreExpList(data);
      if (res.status != 0) {
        uni.$u.toast(res?.message || "核心经验保存失败");
        return false
      }
      return true
    } catch (err) {
      console.log(err);
      return false
    }
  }
  return true
}

// 用户修改
const userChange = (e, index) => {

}

/**
 * 初始化当前页面数据
 */
function initPageData() {
  /** 初始化字典 */
  getDICT("all").then((dics) => {
    const ObservationRecordSceneEnumDesc =
      dics["ObservationRecordSceneEnumDesc"];
    if (ObservationRecordSceneEnumDesc) {
      optionsList.observationRecordScene = Object.keys(
        dics["ObservationRecordSceneEnumDesc"]
      ).map((key) => {
        return {
          label: ObservationRecordSceneEnumDesc[key],
          id: key,
        };
      });
    }

    getclassList()
      .then((r) => {
        const curClassid = uni.getStorageSync("USER_INFO").currentClassId;
        optionsList.schoolClassId = r.data.map((d) => ({
          ...d,
          label: d.title,
        }));
        formData.value.schoolClassId = optionsList.schoolClassId.find(
          (item) => item.id == curClassid
        )?.id;
      })
      .catch((err) => { });

    if (params.value.id) {
      isPageLoading.value = true;
      // 获取详情
      getObservationDetailById(params.value.id).then((r) => {
        const data = {
          ...r.data,
          observationTime: dayjs(r.data.observationTime).format(
            "YYYY-MM-DD HH:mm:ss"
          ),
        };
        if (data.children) {
          // data.childIds = data.observedChildren.map((v) => v.id);
          data.childNames = data.children.map((v) => v.childName);
          data.schoolClassId = data.children[0].classId;
          // 创建一个 Set 来存储 optionsList.childIds 中的 label 值
          const labelSet = new Set(optionsList.childIds.map((v2) => v2.label));

          // 遍历 data.childNames，如果 labelSet 中存在对应的值，则将 v2.id 添加到 optionsList.ClassnameSelectValue 中
          data.childNames.forEach((v1) => {
            if (labelSet.has(v1)) {
              // 找到对应的 v2 并添加其 id
              const v2 = optionsList.childIds.find((v2) => v2.label === v1);
              if (v2) optionsList.ClassnameSelectValue.push(childrenFilter(v2));
            }
          });
          if (data.picList) {
            data.picList = data.picList.map((m) => {
              console.log(m, "m");
              return getFileType(m);
            });
          }
          console.log(data.picList);

          // data.childNames.forEach((v1) => {
          //   optionsList.childIds.forEach((v2) => {
          //     if (v1 === v2.label) {
          //       optionsList.ClassnameSelectValue.push(v2.id);
          //     }
          //   });
          // });
        }

        if (data.schoolClassId) {
          updateChildList();
        }
        formData.value = getInitFormData({
          ...data,
        });
        isPageLoading.value = false;
      });
    }
  });
  updateChildList();
}

const calendarConfirm = (e) => { };

initPageData();
</script>
<script>
export default {
  options: { styleIsolation: "shared" }, // 解除样式隔离
};
</script>

<style lang="scss" scoped>
.uploadPlaceholder {
  padding: 0;
  margin-bottom: 20rpx;
  text-align: left;
  color: rgb(192, 196, 204);
  font-size: 30rpx;
  font-weight: 400;
}

.matrixSelect-add {
  color: #3F79FF;
  font-size: 30rpx;
  font-weight: 500;
  height: 110rpx;
  line-height: 110rpx;
  text-align: center;
  border-radius: 28rpx;
  background: #FFF;
  margin-bottom: 32rpx;

  &:active {
    background: #F5F5F5
  }
}

.matrixSelect-title {
  margin: 48rpx 0 32rpx 0;
}

.child {
  position: relative;

  .mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.6);
    z-index: 999;
    border-radius: 28rpx;
  }
}

:deep(.u-textarea) {
  padding: 0 !important;
}

:deep(.u-form-item__body__left__content) {
  flex-direction: row-reverse;
  flex: initial;

  .u-form-item__body__left__content__required {
    right: 0;
    left: initial;
  }
}

.addObservation-form {
  .datetime-picker {
    :deep(.uniui-calendar) {
      display: none;
    }

    :deep(.uni-date__x-input) {
      color: black;
    }

    :deep(.uni-date__x-input-placeholder) {
      color: rgb(192, 196, 204);
      font-size: 30rpx;
      font-weight: 400;
      padding-left: 0;
    }
  }
}

.placeholder {
  color: rgb(192, 196, 204);
  font-size: 30rpx;
  font-weight: 400;
}

.addObservation-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  width: calc(100vw - 48rpx);
  padding: 12rpx 24rpx;
  padding-bottom: calc(env(safe-area-inset-bottom) + 12rpx);
  height: 80rpx;
  line-height: 80rpx;
  background: #fff;

  .addObservation-button-save {
    background: #3e82f4;
    color: #fff;
    width: 100%;
    height: 100%;
    text-align: center;
    border-radius: 44rpx;
  }
}

.addObservation {
  padding: 0 32rpx 202rpx 32rpx;
  // #ifdef H5
  padding-bottom: 136rpx;

  // #endif
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    font-size: 24rpx;

    .addObservation-title-right-box {
      display: flex;
      align-items: center;
      font-size: 26rpx;
      font-weight: 400;
      color: rgba(128, 128, 128, 1);

      image {
        margin-right: 10rpx;
        margin-top: 5rpx;
      }
    }

    .addObservation-title-right {
      width: 32rpx;
      height: 32rpx;
      margin-left: 12rpx;
    }
  }
}

.matric-title {
  height: 80rpx;
  line-height: 80rpx;
}

.selectable-content {
  width: 100%;
  height: 110rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  color: #3f79ff;
  font-size: 30rpx;
  font-weight: 500;
  border-radius: 28rpx;
}
</style>
